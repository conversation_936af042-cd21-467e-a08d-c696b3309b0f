<script setup lang="ts">
import { AutoCleanup } from '@/utils/transfer';
import checkUpdate from '@/utils/check-update';

onLaunch(() => {
  console.log('App Lauch');

  // 启动数据传递自动清理（每5分钟清理一次过期数据）
  AutoCleanup.start(5 * 60 * 1000);

  // #ifdef MP-WEIXIN
  checkUpdate.checkMpUpdate();
  // #endif

  // #ifdef APP-PLUS
  setTimeout(() => {
    checkUpdate.checkApp({}, true);
  }, 8000);
  // #endif
});
onShow(() => {
  console.log('App Show');
});
onHide(() => {
  console.log('App Hide');
});
</script>

<style lang="scss">
/* 每个页面公共css */
@import '@/static/styles/common.scss';
</style>
