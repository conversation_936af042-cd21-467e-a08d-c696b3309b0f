/**
 * 性能监控工具
 */

interface PerformanceRecord {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
}

class PerformanceMonitor {
  private records: Map<string, PerformanceRecord> = new Map();
  private globalStartTime: number = Date.now();

  constructor() {
    console.log(`[性能监控] 全局监控器初始化: ${this.globalStartTime}`);
  }

  /**
   * 开始监控一个操作
   */
  start(name: string): void {
    const startTime = Date.now();
    this.records.set(name, {
      name,
      startTime,
    });
    console.log(`[性能监控] ${name} 开始: ${startTime - this.globalStartTime}ms`);
  }

  /**
   * 结束监控一个操作
   */
  end(name: string): number {
    const record = this.records.get(name);
    if (!record) {
      console.warn(`[性能监控] 未找到监控记录: ${name}`);
      return 0;
    }

    const endTime = Date.now();
    const duration = endTime - record.startTime;
    
    record.endTime = endTime;
    record.duration = duration;

    console.log(`[性能监控] ${name} 完成: ${endTime - this.globalStartTime}ms, 耗时: ${duration}ms`);
    return duration;
  }

  /**
   * 记录一个时间点
   */
  mark(name: string): void {
    const time = Date.now();
    console.log(`[性能监控] ${name}: ${time - this.globalStartTime}ms`);
  }

  /**
   * 获取所有监控记录
   */
  getRecords(): PerformanceRecord[] {
    return Array.from(this.records.values());
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const records = this.getRecords();
    let report = '\n=== 性能监控报告 ===\n';
    
    records.forEach(record => {
      if (record.duration !== undefined) {
        report += `${record.name}: ${record.duration}ms\n`;
      }
    });
    
    report += '==================\n';
    return report;
  }

  /**
   * 清空所有记录
   */
  clear(): void {
    this.records.clear();
    this.globalStartTime = Date.now();
    console.log(`[性能监控] 监控记录已清空，重新开始计时`);
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();

// 导出便捷方法
export const startMonitor = (name: string) => performanceMonitor.start(name);
export const endMonitor = (name: string) => performanceMonitor.end(name);
export const markTime = (name: string) => performanceMonitor.mark(name);
export const getPerformanceReport = () => performanceMonitor.generateReport();

// 页面生命周期监控装饰器
export function monitorPageLifecycle(pageName: string) {
  return {
    onLoad: () => {
      startMonitor(`${pageName}_onLoad`);
    },
    onReady: () => {
      endMonitor(`${pageName}_onLoad`);
      markTime(`${pageName}_onReady`);
    },
    onShow: () => {
      markTime(`${pageName}_onShow`);
    },
    onHide: () => {
      markTime(`${pageName}_onHide`);
    }
  };
}

// API请求监控装饰器
export function monitorApiCall<T extends (...args: any[]) => Promise<any>>(
  apiName: string,
  apiFunction: T
): T {
  return (async (...args: any[]) => {
    startMonitor(`API_${apiName}`);
    try {
      const result = await apiFunction(...args);
      endMonitor(`API_${apiName}`);
      return result;
    } catch (error) {
      endMonitor(`API_${apiName}`);
      console.error(`[性能监控] API ${apiName} 执行失败:`, error);
      throw error;
    }
  }) as T;
}
