#!/usr/bin/env node

/**
 * HBuilder CLI APP打包脚本
 * 与HBuilderX可视化界面保持一致的云打包流程
 */

import { exec } from 'node:child_process';
import { promisify } from 'node:util';
import { resolve } from 'node:path';
import { writeFileSync } from 'node:fs';

const execAsync = promisify(exec);

// 获取命令行参数
const args = process.argv.slice(2);
const mode = args.find(arg => arg.startsWith('--mode='))?.split('=')[1] || 'development';
const platform = args.find(arg => arg.startsWith('--platform='))?.split('=')[1] || 'android';

console.log(`🚀 开始构建APP - 环境: ${mode}, 平台: ${platform}`);

/**
 * 生成HBuilderX CLI配置文件
 */
function generateHBuilderConfig(config, platform) {
  const hbuilderConfig = {
    project: config.project,
    platform: platform,
    iscustom: config.iscustom,
    safemode: config.safemode,
    isconfusion: config.isconfusion,
    splashads: config.splashads,
    rpads: config.rpads,
    pushads: config.pushads,
    exchange: config.exchange,
  };

  // 添加平台特定配置
  if (platform === 'android') {
    hbuilderConfig.android = config.android;
  } else if (platform === 'ios') {
    hbuilderConfig.ios = config.ios;
  }

  // 写入配置文件
  const configFilePath = resolve(process.cwd(), 'HBuilderConfig.json');
  writeFileSync(configFilePath, JSON.stringify(hbuilderConfig, null, 2));
  console.log('📝 已生成 HBuilderConfig.json 配置文件');

  return configFilePath;
}

/**
 * 执行构建命令
 */
async function buildApp() {
  try {
    // 1. 读取配置文件
    const configPath = resolve(process.cwd(), 'hbuilder.config.js');
    const { default: getConfig } = await import(`file://${configPath}`);
    const config = getConfig(mode);

    // 2. 生成HBuilderX配置文件
    const configFilePath = generateHBuilderConfig(config, platform);

    // 3. 构建HBuilder CLI命令
    const hbuilderCommand = buildHBuilderCommand(configFilePath, platform, config);

    // 4. 执行HBuilder CLI打包
    if (hbuilderCommand) {
      console.log('📱 执行HBuilder CLI打包...');
      console.log(`命令: ${hbuilderCommand}`);

      if (process.env.NODE_ENV !== 'test') {
        await execAsync(hbuilderCommand);
        console.log('✅ APP打包完成!');
        console.log('📱 请在HBuilderX中查看打包状态和下载链接');
      } else {
        console.log('🧪 测试模式，跳过实际打包');
      }
    }
  } catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
  }
}

/**
 * 构建HBuilder CLI命令
 */
function buildHBuilderCommand(configFilePath, platform, config) {
  const commands = ['cli', 'pack'];

  // 使用配置文件
  commands.push('--config', configFilePath);

  // 平台参数
  commands.push('--platform', platform);

  // 是否自定义基座
  if (config.iscustom) {
    commands.push('--iscustom', 'true');
  } else {
    commands.push('--iscustom', 'false');
  }

  // 安心打包
  if (config.safemode) {
    commands.push('--safemode', 'true');
  } else {
    commands.push('--safemode', 'false');
  }

  return commands.join(' ');
}

// 执行构建
buildApp();
