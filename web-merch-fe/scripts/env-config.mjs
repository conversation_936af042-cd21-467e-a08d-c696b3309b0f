#!/usr/bin/env node

/**
 * 环境配置管理脚本
 * 用于管理不同环境的配置文件
 */

import { readFileSync, writeFileSync, existsSync } from 'node:fs';
import { resolve } from 'node:path';

// 支持的环境列表
const ENVIRONMENTS = ['development', 'production'];

// 获取命令行参数
const args = process.argv.slice(2);
const command = args[0];
const environment = args[1];

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
环境配置管理工具

用法:
  node scripts/env-config.mjs <command> [environment]

命令:
  list                    列出所有环境配置
  show <env>             显示指定环境的配置
  validate <env>         验证指定环境的配置
  copy <from> <to>       复制环境配置

支持的环境:
  ${ENVIRONMENTS.join(', ')}

示例:
  node scripts/env-config.mjs list
  node scripts/env-config.mjs show development
  node scripts/env-config.mjs validate production
  node scripts/env-config.mjs copy development production
`);
}

/**
 * 列出所有环境配置
 */
function listEnvironments() {
  console.log('📋 环境配置列表:\n');

  ENVIRONMENTS.forEach(env => {
    const envFile = resolve(process.cwd(), `.env.${env}`);
    const exists = existsSync(envFile);
    const status = exists ? '✅' : '❌';

    console.log(`${status} ${env.padEnd(12)} ${exists ? envFile : '配置文件不存在'}`);
  });
}

/**
 * 显示指定环境的配置
 */
function showEnvironment(env) {
  if (!ENVIRONMENTS.includes(env)) {
    console.error(`❌ 不支持的环境: ${env}`);
    return;
  }

  const envFile = resolve(process.cwd(), `.env.${env}`);

  if (!existsSync(envFile)) {
    console.error(`❌ 配置文件不存在: ${envFile}`);
    return;
  }

  console.log(`📄 ${env} 环境配置:\n`);
  const content = readFileSync(envFile, 'utf-8');
  console.log(content);
}

/**
 * 验证环境配置
 */
function validateEnvironment(env) {
  if (!ENVIRONMENTS.includes(env)) {
    console.error(`❌ 不支持的环境: ${env}`);
    return false;
  }

  const envFile = resolve(process.cwd(), `.env.${env}`);

  if (!existsSync(envFile)) {
    console.error(`❌ 配置文件不存在: ${envFile}`);
    return false;
  }

  const content = readFileSync(envFile, 'utf-8');
  const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));

  // 必需的配置项
  const requiredKeys = [
    'VITE_APP_ENV',
    'VITE_API_URL',
    'VITE_SELF_WEBVIEW_URL'
  ];

  const missingKeys = [];
  const foundKeys = [];

  requiredKeys.forEach(key => {
    const found = lines.some(line => line.startsWith(`${key}=`) || line.startsWith(`${key} =`));
    if (found) {
      foundKeys.push(key);
    } else {
      missingKeys.push(key);
    }
  });

  console.log(`🔍 验证 ${env} 环境配置:\n`);

  if (foundKeys.length > 0) {
    console.log('✅ 已配置的项目:');
    foundKeys.forEach(key => console.log(`   - ${key}`));
    console.log('');
  }

  if (missingKeys.length > 0) {
    console.log('❌ 缺少的配置项:');
    missingKeys.forEach(key => console.log(`   - ${key}`));
    console.log('');
    return false;
  }

  console.log('✅ 配置验证通过!');
  return true;
}

/**
 * 复制环境配置
 */
function copyEnvironment(fromEnv, toEnv) {
  if (!ENVIRONMENTS.includes(fromEnv) || !ENVIRONMENTS.includes(toEnv)) {
    console.error(`❌ 不支持的环境: ${fromEnv} 或 ${toEnv}`);
    return;
  }

  const fromFile = resolve(process.cwd(), `.env.${fromEnv}`);
  const toFile = resolve(process.cwd(), `.env.${toEnv}`);

  if (!existsSync(fromFile)) {
    console.error(`❌ 源配置文件不存在: ${fromFile}`);
    return;
  }

  if (existsSync(toFile)) {
    console.warn(`⚠️ 目标配置文件已存在: ${toFile}`);
    console.log('是否覆盖? (y/N)');
    // 这里可以添加交互式确认
  }

  const content = readFileSync(fromFile, 'utf-8');
  const updatedContent = content.replace(
    new RegExp(`VITE_APP_ENV = '${fromEnv}'`, 'g'),
    `VITE_APP_ENV = '${toEnv}'`
  );

  writeFileSync(toFile, updatedContent);
  console.log(`✅ 已复制 ${fromEnv} 配置到 ${toEnv}`);
}

// 主逻辑
switch (command) {
  case 'list':
    listEnvironments();
    break;
  case 'show':
    if (!environment) {
      console.error('❌ 请指定环境名称');
      showHelp();
      process.exit(1);
    }
    showEnvironment(environment);
    break;
  case 'validate':
    if (!environment) {
      console.error('❌ 请指定环境名称');
      showHelp();
      process.exit(1);
    }
    const isValid = validateEnvironment(environment);
    process.exit(isValid ? 0 : 1);
    break;
  case 'copy':
    const toEnv = args[2];
    if (!environment || !toEnv) {
      console.error('❌ 请指定源环境和目标环境');
      showHelp();
      process.exit(1);
    }
    copyEnvironment(environment, toEnv);
    break;
  default:
    showHelp();
    break;
}
