### 使用uniapp+vite+vue3+wot-design-uni 搭建

使用uniapp+vite+vue3+typescript+wot-design-uni 搭建

### 目录结构
```
uniapp-vue3-project
├ build                 vite插件统一管理
│  ├ vite
├ scripts               一些脚本
│  └ verifyCommit.js
├ src
│  ├ api                接口管理
│  ├ components         公共组件
│  ├ hooks              常用hooks封装
│  ├ pages              页面管理
│  ├ static             静态资源
│  ├ store              状态管理
│  ├ utils              一些工具
│  ├ App.vue
│  ├ main.ts
│  ├ manifest.json
│  ├ pages.json
│  ├ permission.ts      页面访问权限控制
│  └ uni.scss
├ types                 全局typescript类型文件
│  ├ auto-imports.d.ts
│  ├ components.d.ts
│  ├ global.d.ts
│  └ module.d.ts
├ README.md
├ eslint.config.js
├ index.html
├ package.json
├ pnpm-lock.yaml
├ tsconfig.json
├ uno.config.ts
└ vite.config.ts
```

#### vite插件管理
```
build
├ vite
│  ├ plugins
│  │  ├ autoImport.ts  自动导入api
│  │  ├ component.ts   自动导入组件
│  │  ├ imagemin.ts    图片压缩
│  │  ├ index.ts       入口文件
│  │  └ unocss.ts      unocss插件
```

#### 接口管理
```
api
├ common       通用api
│  ├ index.ts
│  └ types.ts
├ user         用户相关api
│  ├ index.ts
│  └ types.ts
└ index.ts     入口文件
```

#### hooks管理
```
hooks
├ use-clipboard  剪切板
│  └ index.ts
├ use-loading    loading
│  └ index.ts
├ use-modal      模态框
│  └ index.ts
├ use-share      分享
│  └ index.ts
└ index.ts       入口文件
```

### 页面管理
```
pages
├ common           公共页面（分包common）
│  ├ login
│  │  └ index.vue
│  └ webview
│     └ index.vue
└ tab              主页面（主包）
   ├ home
   │  └ index.vue
```

#### 状态管理
```
store
├ modules
│  ├ app          app状态
│  │  ├ index.ts
│  │  └ types.ts
│  └ user         用户状态
│     ├ index.ts
│     └ types.ts
└ index.ts        入口文件
```

### 工具方法
```
utils
├ auth                token相关方法
│  └ index.ts
├ common              通用方法
│  └ index.ts
├ modals              弹窗相关方法
│  └ index.ts
├ request             网络请求相关方法
│  ├ index.ts
│  ├ interceptors.ts
│  ├ status.ts
│  └ type.ts
└ index.ts            入口文件
```

### 使用方法

```bash
# 安装依赖
pnpm install

# 启动H5
pnpm dev:h5

# 启动微信小程序
pnpm dev:mp-weixin
```

### 发布

#### H5和小程序构建
```bash
# 构建测试环境
pnpm build:h5
pnpm build:mp-weixin

# 构建生产环境
pnpm build:h5-prod
pnpm build:mp-weixin-prod

# 构建其他环境
pnpm build:h5 --mode test
pnpm build:h5 --mode staging
```

#### APP构建 (HBuilder CLI)

```bash
# APP打包
pnpm hbuilder:android:dev      # Android开发包
pnpm hbuilder:android:prod     # Android生产包
pnpm hbuilder:ios:dev          # iOS开发包
pnpm hbuilder:ios:prod         # iOS生产包

# 环境配置管理
pnpm env:list                  # 查看所有环境配置
pnpm env:validate production   # 验证环境配置
```

##### 打包配置说明

**配置文件结构**：
- `hbuilder.config.js` - HBuilder CLI打包配置（证书、安心打包等）
- `src/manifest.json` - 应用功能配置（SDK、权限、图标等）
- `.env.production` - 生产环境证书配置

**证书配置示例**：
```bash
# Android证书
ANDROID_PACKAGE_NAME=com.xnwcloud.wsapp
ANDROID_KEY_ALIAS=your_alias
ANDROID_KEYSTORE_PATH=/path/to/your.keystore
ANDROID_KEYSTORE_PASSWORD=your_password

# iOS证书
IOS_BUNDLE_ID=com.xnwcloud.wsapp
IOS_SUPPORTED_DEVICE=iPhone
IOS_PROFILE_PATH=/path/to/profile.mobileprovision
IOS_DIST_CERT_PATH=/path/to/dist.p12
IOS_DIST_CERT_PASSWORD=cert_password
```

**关键特性**：
- ✅ **安心打包** - 默认启用安心打包模式
- ✅ **自动配置** - 自动生成HBuilderConfig.json配置文件
- ✅ **证书管理** - 支持开发/发布证书自动切换
- ✅ **与HBuilderX一致** - 配置参数与可视化界面保持一致

> **配置原则**: 打包相关参数在hbuilder.config.js中配置，功能配置直接在manifest.json中处理，避免重复配置。

### 代码提交
```bash
pnpm cz
```

### 更新uniapp版本

更新uniapp相关依赖到最新正式版
```bash
npx @dcloudio/uvm@latest
```
