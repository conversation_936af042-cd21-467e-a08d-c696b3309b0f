{"name": "web-merch-fe", "version": "1.0.0", "license": "MIT", "engines": {"node": ">=16", "pnpm": ">=8"}, "scripts": {"dev:h5": "uni", "dev:h5-prod": "uni --mode production", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-weixin-prod": "uni -p mp-weixin --mode production", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "build:h5": "uni build", "build:h5-prod": "uni build  --mode production", "build:mp-weixin": "uni build -p mp-weixin --mode development", "build:mp-weixin-prod": "uni build -p mp-weixin --mode production", "build:app": "uni build -p app --mode development", "build:app-prod": "uni build -p app --mode production", "build:app-android": "uni build -p app-android --mode development", "build:app-android-prod": "uni build -p app-android --mode production", "build:app-ios": "uni build -p app-ios --mode development", "build:app-ios-prod": "uni build -p app-ios --mode production", "hbuilder:android:dev": "node scripts/build-app.mjs --mode=development --platform=android", "hbuilder:android:prod": "node scripts/build-app.mjs --mode=production --platform=android --upload", "hbuilder:ios:dev": "node scripts/build-app.mjs --mode=development --platform=ios", "hbuilder:ios:prod": "node scripts/build-app.mjs --mode=production --platform=ios --upload", "type-check": "vue-tsc --noEmit", "eslint": "eslint \"src/**/*.{js,jsx,ts,tsx,vue}\" --fix", "stylelint": "stylelint \"src/**/*.(vue|scss|css)\" --fix", "cz": "git add . && npx czg", "postinstall": "simple-git-hooks", "uvm": "npx @dcloudio/uvm@latest alpha", "uvm-rm": "node ./scripts/postupgrade.mjs", "env:list": "node scripts/env-config.mjs list", "env:show": "node scripts/env-config.mjs show", "env:validate": "node scripts/env-config.mjs validate", "env:copy": "node scripts/env-config.mjs copy"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@dcloudio/uni-app": "3.0.0-alpha-4030220241101001", "@dcloudio/uni-app-plus": "3.0.0-alpha-4030220241101001", "@dcloudio/uni-components": "3.0.0-alpha-4030220241101001", "@dcloudio/uni-h5": "3.0.0-alpha-4030220241101001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-4030220241101001", "@types/sm-crypto": "^0.3.4", "dayjs": "^1.11.12", "image-tools": "^1.4.0", "js-base64": "^3.7.7", "luch-request": "^3.1.1", "mitt": "^3.0.1", "number-precision": "^1.6.0", "pinia": "2.0.36", "pinia-plugin-persist-uni": "^1.3.1", "sm-crypto": "^0.3.13", "vue": "3.4.21", "vue-qrcode-reader": "^5.5.11", "weixin-js-sdk": "^1.6.5", "wot-design-uni": "^1.5.1", "z-paging": "^2.7.11"}, "devDependencies": {"@antfu/eslint-config": "2.21.1", "@dcloudio/types": "^3.4.12", "@dcloudio/uni-automator": "3.0.0-alpha-4030220241101001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-4030220241101001", "@dcloudio/uni-stacktracey": "3.0.0-alpha-4030220241101001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-4030220241101001", "@esbuild/darwin-arm64": "0.24.0", "@esbuild/darwin-x64": "0.24.0", "@iconify-json/mdi": "^1.1.66", "@rollup/rollup-darwin-x64": "4.20.0", "@types/node": "^20.14.10", "@typescript-eslint/parser": "^7.15.0", "@uni-helper/uni-app-types": "0.5.13", "@unocss/eslint-plugin": "^0.61.0", "@unocss/preset-icons": "^0.61.0", "@vue/runtime-core": "^3.5.8", "@vue/tsconfig": "^0.5.1", "czg": "^1.9.2", "eslint": "^8.57.0", "lint-staged": "^15.2.5", "miniprogram-api-typings": "^3.12.2", "picocolors": "^1.0.1", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.5", "simple-git-hooks": "^2.11.1", "stylelint": "^16.6.1", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^6.0.3", "typescript": "^5.4.5", "unocss": "^0.58.9", "unocss-applet": "^0.7.8", "unplugin-auto-import": "^0.18.0", "unplugin-vue-components": "^0.27.2", "vite": "^5.2.8", "vite-plugin-require-transform": "^1.0.21", "vue-tsc": "^2.0.26"}, "resolutions": {"esbuild": "npm:esbuild@latest"}, "simple-git-hooks": {"commit-msg": "node web-merch-fe/scripts/verifyCommit.mjs"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": "eslint --fix", "*.{scss,less,style,html}": "stylelint --fix", "*.vue": ["eslint --fix", "stylelint --fix"]}}