<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>万商云Pro - App Store 截图模板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f0f0;
            padding: 20px;
        }
        
        .template-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            justify-content: center;
        }
        
        /* iPhone 6.7" (1290x2796) */
        .iphone-67 {
            width: 387px;  /* 1290/3.33 for display */
            height: 839px; /* 2796/3.33 for display */
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
            background: transparent;
        }
        
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
        }
        
        .content {
            padding: 20px;
            height: calc(100% - 44px);
            overflow-y: auto;
        }
        
        .app-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .app-logo {
            width: 80px;
            height: 80px;
            border-radius: 18px;
            background: linear-gradient(135deg, #4d80f0, #6366f1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 32px;
            font-weight: bold;
            box-shadow: 0 8px 25px rgba(77, 128, 240, 0.3);
        }
        
        .app-title {
            font-size: 24px;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 8px;
        }
        
        .app-subtitle {
            font-size: 16px;
            color: #666;
            line-height: 1.4;
        }
        
        .feature-list {
            margin-top: 40px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: 20px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: linear-gradient(135deg, #4d80f0, #6366f1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-right: 16px;
            flex-shrink: 0;
        }
        
        .feature-content {
            flex: 1;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 4px;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #4d80f0, #6366f1);
            border-radius: 16px;
            padding: 24px;
            color: white;
            margin: 20px 0;
            box-shadow: 0 8px 25px rgba(77, 128, 240, 0.3);
        }
        
        .stats-title {
            font-size: 16px;
            margin-bottom: 16px;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .cta-section {
            text-align: center;
            margin-top: 40px;
            padding: 30px 20px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .cta-title {
            font-size: 20px;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 12px;
        }
        
        .cta-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 24px;
            line-height: 1.5;
        }
        
        .cta-button {
            background: linear-gradient(135deg, #4d80f0, #6366f1);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 32px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(77, 128, 240, 0.3);
            transition: transform 0.2s;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
        }
        
        .template-label {
            position: absolute;
            top: -30px;
            left: 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            background: white;
            padding: 4px 12px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .download-section {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .download-title {
            font-size: 24px;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 16px;
        }
        
        .download-desc {
            font-size: 16px;
            color: #666;
            margin-bottom: 24px;
            line-height: 1.5;
        }
        
        .download-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .download-btn {
            background: #4d80f0;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: background 0.2s;
        }
        
        .download-btn:hover {
            background: #3d70e0;
        }
    </style>
</head>
<body>
    <div class="download-section">
        <h1 class="download-title">万商云Pro App Store 屏幕截图模板</h1>
        <p class="download-desc">
            以下是为 App Store 审核准备的屏幕截图模板，符合苹果官方要求的尺寸规格。<br>
            右键点击每个模板保存为图片，或使用浏览器的截图工具。
        </p>
        <div class="download-buttons">
            <a href="#" class="download-btn" onclick="captureScreenshot('template1')">保存模板1</a>
            <a href="#" class="download-btn" onclick="captureScreenshot('template2')">保存模板2</a>
            <a href="#" class="download-btn" onclick="captureScreenshot('template3')">保存模板3</a>
        </div>
    </div>
    
    <div class="template-container">
        <!-- 模板1: 应用介绍 -->
        <div class="iphone-67" id="template1">
            <div class="template-label">模板1: 应用介绍 (1290x2796)</div>
            <div class="screen">
                <div class="dynamic-island"></div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                
                <div class="content">
                    <div class="app-header">
                        <div class="app-logo">万</div>
                        <h1 class="app-title">万商云Pro</h1>
                        <p class="app-subtitle">专业的商户管理平台<br>让支付更简单，让管理更高效</p>
                    </div>
                    
                    <div class="feature-list">
                        <div class="feature-item">
                            <div class="feature-icon">📋</div>
                            <div class="feature-content">
                                <h3 class="feature-title">商户报备</h3>
                                <p class="feature-desc">快速完成商户信息报备，支持个人和企业认证</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">💳</div>
                            <div class="feature-content">
                                <h3 class="feature-title">交易管理</h3>
                                <p class="feature-desc">实时查看交易记录，支持多种支付方式统计</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">🖥️</div>
                            <div class="feature-content">
                                <h3 class="feature-title">设备管理</h3>
                                <p class="feature-desc">便捷管理POS终端设备，远程监控设备状态</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">💰</div>
                            <div class="feature-content">
                                <h3 class="feature-title">结算管理</h3>
                                <p class="feature-desc">智能结算卡管理，资金流向清晰透明</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="cta-section">
                        <h3 class="cta-title">立即开始使用</h3>
                        <p class="cta-desc">加入万商云Pro，体验专业的商户管理服务</p>
                        <button class="cta-button">免费注册</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 模板2: 功能展示 -->
        <div class="iphone-67" id="template2">
            <div class="template-label">模板2: 功能展示 (1290x2796)</div>
            <div class="screen">
                <div class="dynamic-island"></div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                
                <div class="content">
                    <div class="app-header">
                        <div class="app-logo">📊</div>
                        <h1 class="app-title">实时数据统计</h1>
                        <p class="app-subtitle">全面掌握业务数据，助力决策分析</p>
                    </div>
                    
                    <div class="stats-card">
                        <div class="stats-title">今日交易概览</div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">¥12,580</div>
                                <div class="stat-label">交易总额</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">28</div>
                                <div class="stat-label">交易笔数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">¥12,455</div>
                                <div class="stat-label">结算金额</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="feature-list">
                        <div class="feature-item">
                            <div class="feature-icon">📈</div>
                            <div class="feature-content">
                                <h3 class="feature-title">数据分析</h3>
                                <p class="feature-desc">多维度数据分析，趋势图表一目了然</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">🔔</div>
                            <div class="feature-content">
                                <h3 class="feature-title">实时通知</h3>
                                <p class="feature-desc">交易状态实时推送，重要信息不错过</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">🛡️</div>
                            <div class="feature-content">
                                <h3 class="feature-title">安全保障</h3>
                                <p class="feature-desc">银行级安全加密，资金安全有保障</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">📱</div>
                            <div class="feature-content">
                                <h3 class="feature-title">移动办公</h3>
                                <p class="feature-desc">随时随地管理业务，提升工作效率</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 模板3: 用户体验 -->
        <div class="iphone-67" id="template3">
            <div class="template-label">模板3: 用户体验 (1290x2796)</div>
            <div class="screen">
                <div class="dynamic-island"></div>
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                
                <div class="content">
                    <div class="app-header">
                        <div class="app-logo">⭐</div>
                        <h1 class="app-title">用户好评如潮</h1>
                        <p class="app-subtitle">数万商户的共同选择，值得信赖的合作伙伴</p>
                    </div>
                    
                    <div class="stats-card">
                        <div class="stats-title">用户数据</div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">50,000+</div>
                                <div class="stat-label">注册商户</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">4.8</div>
                                <div class="stat-label">用户评分</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">99.9%</div>
                                <div class="stat-label">系统稳定性</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="feature-list">
                        <div class="feature-item">
                            <div class="feature-icon">🎯</div>
                            <div class="feature-content">
                                <h3 class="feature-title">精准定位</h3>
                                <p class="feature-desc">专为中小商户设计，功能实用易上手</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">🚀</div>
                            <div class="feature-content">
                                <h3 class="feature-title">快速部署</h3>
                                <p class="feature-desc">5分钟完成注册，即刻开始使用</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">💬</div>
                            <div class="feature-content">
                                <h3 class="feature-title">专业客服</h3>
                                <p class="feature-desc">7×24小时在线客服，问题及时解决</p>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">🔄</div>
                            <div class="feature-content">
                                <h3 class="feature-title">持续更新</h3>
                                <p class="feature-desc">定期功能更新，始终保持领先体验</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="cta-section">
                        <h3 class="cta-title">加入我们</h3>
                        <p class="cta-desc">成为万商云Pro的一员，开启智能商户管理新时代</p>
                        <button class="cta-button">立即下载</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function captureScreenshot(templateId) {
            // 这里可以添加截图功能的实现
            // 或者提示用户使用浏览器的截图功能
            alert(`请右键点击 ${templateId} 模板，选择"将图像另存为"来保存截图`);
        }
        
        // 添加一些交互效果
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-2px)';
                }, 100);
            });
        });
    </script>
</body>
</html>
