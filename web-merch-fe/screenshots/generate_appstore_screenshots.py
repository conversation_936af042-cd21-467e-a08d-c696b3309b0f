#!/usr/bin/env python3
"""
万商云Pro App Store 屏幕截图生成器
生成符合 App Store 审核要求的屏幕截图
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from PIL import Image
import io

# App Store 要求的屏幕截图尺寸
SCREENSHOT_SIZES = {
    'iPhone_6_7': (1290, 2796),  # iPhone 14 Pro Max, 15 Pro Max
    'iPhone_6_5': (1242, 2688),  # iPhone 11 Pro Max, 12 Pro Max, 13 Pro Max  
    'iPhone_5_5': (1242, 2208),  # iPhone 6s Plus, 7 Plus, 8 Plus
}

def setup_driver():
    """设置 Chrome 驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1290,2796')
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"Chrome 驱动设置失败: {e}")
        print("请确保已安装 Chrome 浏览器和 ChromeDriver")
        return None

def capture_screenshot(driver, element_id, filename, size):
    """捕获指定元素的屏幕截图"""
    try:
        # 等待元素加载
        element = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, element_id))
        )
        
        # 滚动到元素位置
        driver.execute_script("arguments[0].scrollIntoView();", element)
        time.sleep(1)
        
        # 获取元素截图
        screenshot = element.screenshot_as_png
        
        # 使用 PIL 处理图片
        image = Image.open(io.BytesIO(screenshot))
        
        # 调整到指定尺寸
        image = image.resize(size, Image.Resampling.LANCZOS)
        
        # 保存图片
        image.save(filename, 'PNG', quality=95)
        print(f"✅ 已生成: {filename}")
        
    except Exception as e:
        print(f"❌ 生成 {filename} 失败: {e}")

def generate_screenshots():
    """生成所有屏幕截图"""
    # 创建输出目录
    output_dir = "appstore_screenshots"
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置驱动
    driver = setup_driver()
    if not driver:
        return
    
    try:
        # 加载 HTML 文件
        html_file = os.path.abspath("generate_screenshots.html")
        driver.get(f"file://{html_file}")
        
        # 等待页面加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "phone-frame"))
        )
        
        # 屏幕截图配置
        screenshots_config = [
            {
                'element_id': 'login-screen',
                'name': '01_login',
                'title': '登录页面'
            },
            {
                'element_id': 'home-screen', 
                'name': '02_home',
                'title': '首页'
            },
            {
                'element_id': 'transaction-screen',
                'name': '03_transactions', 
                'title': '交易管理'
            }
        ]
        
        # 为每个尺寸生成截图
        for size_name, size in SCREENSHOT_SIZES.items():
            size_dir = os.path.join(output_dir, size_name)
            os.makedirs(size_dir, exist_ok=True)
            
            print(f"\n📱 生成 {size_name} ({size[0]}x{size[1]}) 尺寸截图:")
            
            for config in screenshots_config:
                filename = os.path.join(size_dir, f"{config['name']}.png")
                capture_screenshot(driver, config['element_id'], filename, size)
        
        print(f"\n🎉 所有屏幕截图已生成完成！")
        print(f"📁 输出目录: {os.path.abspath(output_dir)}")
        
    except Exception as e:
        print(f"❌ 生成截图时出错: {e}")
    
    finally:
        driver.quit()

def create_readme():
    """创建说明文档"""
    readme_content = """# 万商云Pro App Store 屏幕截图

## 文件说明

本目录包含为 App Store 审核准备的屏幕截图，符合苹果官方要求的尺寸规格。

### 屏幕截图尺寸

- **iPhone_6_7**: 1290 x 2796 像素 (iPhone 14 Pro Max, 15 Pro Max)
- **iPhone_6_5**: 1242 x 2688 像素 (iPhone 11 Pro Max, 12 Pro Max, 13 Pro Max)  
- **iPhone_5_5**: 1242 x 2208 像素 (iPhone 6s Plus, 7 Plus, 8 Plus)

### 截图内容

1. **01_login.png** - 登录页面
   - 展示用户登录界面
   - 包含商户/机构登录切换
   - 显示输入框和登录按钮

2. **02_home.png** - 应用首页  
   - 展示主要功能菜单
   - 显示交易统计数据
   - 包含商户报备、交易管理等核心功能

3. **03_transactions.png** - 交易管理页面
   - 展示交易列表
   - 显示交易统计信息
   - 包含交易详情和状态

### 使用说明

1. 根据您要提交的设备类型选择对应尺寸的截图
2. 在 App Store Connect 中上传对应的截图文件
3. 确保截图内容清晰，符合苹果审核指南

### App Store 审核要求

- 截图必须使用实际应用内容，不能使用模拟器或设计稿
- 截图尺寸必须精确匹配要求的像素尺寸
- 截图内容应展示应用的核心功能
- 避免包含敏感信息或占位符文本

### 技术规格

- 格式: PNG
- 颜色空间: sRGB 或 P3
- 分辨率: 72 DPI 或更高
- 压缩: 无损压缩
"""
    
    with open("appstore_screenshots/README.md", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("📝 已生成 README.md 说明文档")

if __name__ == "__main__":
    print("🚀 开始生成万商云Pro App Store 屏幕截图...")
    print("=" * 50)
    
    # 检查依赖
    try:
        import selenium
        from PIL import Image
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install selenium pillow")
        exit(1)
    
    # 生成截图
    generate_screenshots()
    
    # 创建说明文档
    create_readme()
    
    print("\n" + "=" * 50)
    print("✨ 完成！现在您可以将生成的截图上传到 App Store Connect")
