<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>万商云Pro - App Store 截图生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f7;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .phone-frame {
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
            aspect-ratio: 1290/2796; /* iPhone 14 Pro Max ratio */
        }
        
        .phone-screen {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-radius: 32px;
            height: 100%;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        
        .navbar {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent;
            position: relative;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4d80f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }
        
        .content {
            padding: 20px;
            height: calc(100% - 104px);
            overflow-y: auto;
        }
        
        .stats-card {
            background: #4d80f0;
            border-radius: 12px;
            padding: 24px;
            color: white;
            margin-bottom: 20px;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-top: 16px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-top: 20px;
        }
        
        .menu-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .menu-item:hover {
            transform: translateY(-2px);
        }
        
        .menu-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: #4d80f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            color: white;
            font-size: 24px;
        }
        
        .menu-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .transaction-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .transaction-item {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .transaction-item:last-child {
            border-bottom: none;
        }
        
        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #846a27;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .transaction-info {
            flex: 1;
        }
        
        .transaction-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .transaction-details {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        
        .transaction-amount {
            text-align: right;
        }
        
        .amount-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .amount-time {
            font-size: 12px;
            color: #999;
            margin-top: 4px;
        }
        
        .login-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 40px 30px;
            height: 100%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            background: #4d80f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            margin-top: 60px;
            margin-bottom: 40px;
        }
        
        .user-type-tabs {
            display: flex;
            background: #f0f0f0;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 40px;
            width: 100%;
        }
        
        .tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .tab.active {
            background: #4d80f0;
            color: white;
        }
        
        .form-group {
            width: 100%;
            margin-bottom: 20px;
        }
        
        .input-field {
            width: 100%;
            padding: 16px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            background: white;
        }
        
        .login-btn {
            width: 100%;
            padding: 16px;
            background: #4d80f0;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            margin-top: 20px;
            cursor: pointer;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #666;
            margin-top: 20px;
        }
        
        .title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }
        
        .subtitle {
            text-align: center;
            font-size: 14px;
            color: #666;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">万商云Pro - App Store 屏幕截图</h1>
        <p class="subtitle">为 App Store 审核准备的标准尺寸屏幕截图</p>
        
        <div class="screenshot-grid">
            <!-- 登录页面 -->
            <div class="phone-frame" id="login-screen">
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 📶 🔋</span>
                    </div>
                    
                    <div class="login-screen">
                        <div class="logo">万</div>
                        
                        <div class="user-type-tabs">
                            <div class="tab active">商户登录</div>
                            <div class="tab">机构登录</div>
                        </div>
                        
                        <div class="form-group">
                            <input type="text" class="input-field" placeholder="请输入账号" value="138****8888">
                        </div>
                        
                        <div class="form-group">
                            <input type="password" class="input-field" placeholder="请输入密码" value="••••••••">
                        </div>
                        
                        <div class="checkbox-group">
                            <input type="checkbox" checked> 记住密码
                            <span style="margin-left: auto; color: #4d80f0;">忘记密码</span>
                        </div>
                        
                        <div class="checkbox-group">
                            <input type="checkbox" checked> 我已阅读《服务协议》和《隐私权规则》并同意服务条款
                        </div>
                        
                        <button class="login-btn">登录</button>
                    </div>
                </div>
            </div>
            
            <!-- 首页 -->
            <div class="phone-frame" id="home-screen">
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 📶 🔋</span>
                    </div>
                    
                    <div class="navbar">
                        <div class="user-info">
                            <div class="user-avatar">👤</div>
                            <span>138****8888</span>
                        </div>
                    </div>
                    
                    <div class="content">
                        <div class="stats-card">
                            <div style="font-size: 16px; margin-bottom: 8px;">今日交易统计</div>
                            <div class="stats-row">
                                <div class="stat-item">
                                    <div class="stat-value">¥12,580</div>
                                    <div class="stat-label">交易总额</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">28</div>
                                    <div class="stat-label">交易笔数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">¥12,455</div>
                                    <div class="stat-label">应结算金额</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="menu-grid">
                            <div class="menu-item">
                                <div class="menu-icon">📋</div>
                                <div class="menu-title">商户报备</div>
                            </div>
                            <div class="menu-item">
                                <div class="menu-icon">💳</div>
                                <div class="menu-title">交易管理</div>
                            </div>
                            <div class="menu-item">
                                <div class="menu-icon">🖥️</div>
                                <div class="menu-title">设备管理</div>
                            </div>
                            <div class="menu-item">
                                <div class="menu-icon">💰</div>
                                <div class="menu-title">结算卡管理</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 交易管理页面 -->
            <div class="phone-frame" id="transaction-screen">
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 📶 🔋</span>
                    </div>
                    
                    <div class="navbar">
                        <span style="font-size: 18px; font-weight: 600;">交易管理</span>
                    </div>
                    
                    <div class="content">
                        <div class="stats-card">
                            <div style="font-size: 14px; margin-bottom: 12px;">2024-01-15</div>
                            <div class="stats-row">
                                <div>
                                    <div style="font-size: 14px; margin-bottom: 4px;">交易总额(元)</div>
                                    <div style="font-size: 20px; font-weight: bold;">12,580.00 <span style="font-size: 12px;">共28笔</span></div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: 14px; margin-bottom: 4px;">应结算金额(元)</div>
                                    <div style="font-size: 20px; font-weight: bold;">12,455.20</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="transaction-list">
                            <div class="transaction-item">
                                <div class="transaction-icon">🏪</div>
                                <div class="transaction-info">
                                    <div class="transaction-name">张三便利店</div>
                                    <div class="transaction-details">
                                        商户号: 123456789<br>
                                        终端: POS001<br>
                                        卡号: ****1234<br>
                                        <span style="color: #999;">2024-01-15 14:30</span>
                                    </div>
                                </div>
                                <div class="transaction-amount">
                                    <div class="amount-value">¥580.00</div>
                                    <div class="amount-time">交易成功</div>
                                </div>
                            </div>
                            
                            <div class="transaction-item">
                                <div class="transaction-icon">🏪</div>
                                <div class="transaction-info">
                                    <div class="transaction-name">李四餐厅</div>
                                    <div class="transaction-details">
                                        商户号: 987654321<br>
                                        终端: POS002<br>
                                        卡号: ****5678<br>
                                        <span style="color: #999;">2024-01-15 13:45</span>
                                    </div>
                                </div>
                                <div class="transaction-amount">
                                    <div class="amount-value">¥1,200.00</div>
                                    <div class="amount-time">交易成功</div>
                                </div>
                            </div>
                            
                            <div class="transaction-item">
                                <div class="transaction-icon">🏪</div>
                                <div class="transaction-info">
                                    <div class="transaction-name">王五超市</div>
                                    <div class="transaction-details">
                                        商户号: 456789123<br>
                                        终端: POS003<br>
                                        卡号: ****9012<br>
                                        <span style="color: #999;">2024-01-15 12:20</span>
                                    </div>
                                </div>
                                <div class="transaction-amount">
                                    <div class="amount-value">¥350.00</div>
                                    <div class="amount-time">交易成功</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 添加一些交互效果
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-2px)';
                }, 100);
            });
        });
        
        // 标签切换效果
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
