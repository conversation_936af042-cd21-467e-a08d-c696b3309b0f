# 环境配置模板文件
# 复制此文件并重命名为对应的环境配置文件

# 环境标识 (development | test | staging | production)
VITE_APP_ENV = 'development'

# API请求的前缀
VITE_API_PREFIX= '/api'

# 后台接口的地址
VITE_API_URL= 'http://localhost:8889/api'

# 项目内嵌自身webview地址
VITE_SELF_WEBVIEW_URL = 'http://localhost:8889'

# 删除console (true | false)
VITE_DROP_CONSOLE = false

# 打包环境标识
VITE_BUILD_ENV = 'local'

# 微信小程序配置
VITE_MP_WEIXIN_PRIVATE_KEY = ''
VITE_MP_WEIXIN_PRIVATE_KEY_PATH = ''

# APP配置
VITE_APP_NAME = '万商云Pro'
VITE_APP_VERSION = '1.0.0'
VITE_APP_VERSION_CODE = '100'

# ========== HBuilder CLI 云打包配置 ==========
# Android打包配置
ANDROID_PACKAGE_NAME = 'com.xnwcloud.wsapp'
ANDROID_KEY_ALIAS = ''
ANDROID_KEYSTORE_PATH = ''
ANDROID_KEYSTORE_PASSWORD = ''
ANDROID_CHANNELS = ''

# iOS打包配置
IOS_BUNDLE_ID = 'com.xnwcloud.wsapp'
IOS_SUPPORTED_DEVICE = 'iPhone'
IOS_PROFILE_PATH = ''

# iOS开发证书
IOS_DEV_CERT_PATH = ''
IOS_DEV_CERT_PASSWORD = ''

# iOS发布证书
IOS_DIST_CERT_PATH = ''
IOS_DIST_CERT_PASSWORD = ''

# 注意：
# 1. SDK配置（微信、高德地图等）直接在 src/manifest.json 中配置
# 2. 权限、图标、启动图等配置也在 src/manifest.json 中处理
# 3. 这里只保留打包相关的证书和基本配置
