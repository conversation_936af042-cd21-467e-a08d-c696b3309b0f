/**
 * HBuilder CLI 云打包配置文件
 * 与HBuilderX可视化界面保持一致的配置参数
 */

const { loadEnv } = require('vite');

module.exports = (mode = 'development') => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd());

  return {
    // 项目名称（与目录名保持一致）
    project: 'web-merch-fe',

    // 打包平台：android, ios
    platform: 'android,ios',

    // 是否使用自定义基座：true-自定义基座, false-自定义证书
    iscustom: false,

    // 打包方式：true-安心打包, false-传统打包
    safemode: true,

    // Android打包参数
    android: {
      // 安卓包名
      packagename: env.ANDROID_PACKAGE_NAME || 'com.xnwcloud.wsapp',

      // 安卓打包类型：0-自有证书 1-公共证书 2-老版证书 3-云端证书
      androidpacktype: '0',

      // 证书别名
      certalias: env.ANDROID_KEY_ALIAS || '',

      // 证书文件路径
      certfile: env.ANDROID_KEYSTORE_PATH || '',

      // 证书密码
      certpassword: env.ANDROID_KEYSTORE_PASSWORD || '',

      // 渠道包：google,yyb,360,huawei,xiaomi,oppo,vivo
      channels: env.ANDROID_CHANNELS || '',
    },

    // iOS打包参数
    ios: {
      // iOS Bundle ID
      bundle: env.IOS_BUNDLE_ID || 'com.xnwcloud.wsapp',

      // 支持的设备类型：iPhone,iPad
      supporteddevice: env.IOS_SUPPORTED_DEVICE || 'iPhone',

      // 是否打越狱包：true-越狱包, false-正式包
      isprisonbreak: false,

      // 描述文件路径
      profile: env.IOS_PROFILE_PATH || '',

      // 证书文件路径
      certfile: mode === 'production'
        ? env.IOS_DIST_CERT_PATH || ''
        : env.IOS_DEV_CERT_PATH || '',

      // 证书密码
      certpassword: mode === 'production'
        ? env.IOS_DIST_CERT_PASSWORD || ''
        : env.IOS_DEV_CERT_PASSWORD || '',
    },

    // 是否混淆：true-混淆, false-关闭
    isconfusion: mode === 'production',

    // 开屏广告：true-打开, false-关闭
    splashads: false,

    // 悬浮红包广告：true-打开, false-关闭
    rpads: false,

    // push广告：true-打开, false-关闭
    pushads: false,

    // 加入换量联盟：true-加入, false-不加入
    exchange: false,
  };
};
